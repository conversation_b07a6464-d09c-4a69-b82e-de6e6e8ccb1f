import { createFileRoute } from "@tanstack/react-router";
import data from "@/app/dashboard/data.json";
import { ChartAreaInteractive } from "@/components/chart-area-interactive";
import { DataTable } from "@/components/data-table";
import { SectionCards } from "@/components/section-cards";
export const Route = createFileRoute("/app/dashboard")({
	component: DashboardRoute,
});

function DashboardRoute() {
	return (
		<div>
			<h1>Dashboard</h1>
			<div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
				<SectionCards />
				<div className="px-4 lg:px-6">
					<ChartAreaInteractive />
				</div>
				<DataTable data={data} />
			</div>
		</div>
	);
}
