import { createFileRoute, Outlet } from "@tanstack/react-router";
import { useEffect } from "react";
import { AppSidebar } from "@/components/app-sidebar";
import { SiteHeader } from "@/components/site-header";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { authClient } from "@/lib/auth-client";

export const Route = createFileRoute("/app")({
	component: AppLayout,
});

function AppLayout() {
	const { data: session, } = authClient.useSession();

	const navigate = Route.useNavigate();

	useEffect(() => {
		if (!session?.user && !isPending) {
			console.log("not signed in", session, isPending);
			navigate({
				to: "/",
			});
		}
	}, [session?.user, isPending]);

	if (isPending) {
		return <div>Loading...</div>;
	}
	return (
		<SidebarProvider
			style={
				{
					"--sidebar-width": "calc(var(--spacing) * 72)",
					"--header-height": "calc(var(--spacing) * 12)",
				} as React.CSSProperties
			}
		>
			<AppSidebar variant="inset" />
			<SidebarInset>
				<SiteHeader />
				<div className="flex flex-col flex-1">
					<div className="@container/main flex flex-1 flex-col gap-2">
						<Outlet />
					</div>
				</div>
			</SidebarInset>
		</SidebarProvider>
	);
}
