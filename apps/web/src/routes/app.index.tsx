import { createFileRoute } from "@tanstack/react-router";

export const Route = createFileRoute("/app/")({
	component: AppIndexRoute,
});

function AppIndexRoute() {
	return (
		<div className="@container/main flex flex-1 flex-col gap-2">
			<div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
				<div className="px-4 lg:px-6">
					<h1 className="font-bold text-2xl">App Home</h1>
					<p className="text-muted-foreground">
						Welcome to your app dashboard.
					</p>
				</div>
				{/* Add your main app content here */}
				<div className="px-4 lg:px-6">
					<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
						<div className="rounded-lg border p-4">
							<h3 className="font-medium">Quick Actions</h3>
							<p className="text-sm text-muted-foreground">
								Access your most used features
							</p>
						</div>
						<div className="rounded-lg border p-4">
							<h3 className="font-medium">Recent Activity</h3>
							<p className="text-sm text-muted-foreground">
								See what's been happening
							</p>
						</div>
						<div className="rounded-lg border p-4">
							<h3 className="font-medium">Analytics</h3>
							<p className="text-sm text-muted-foreground">
								View your performance metrics
							</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
