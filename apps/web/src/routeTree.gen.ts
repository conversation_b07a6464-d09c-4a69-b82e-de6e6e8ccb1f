/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as LoginRouteImport } from './routes/login'
import { Route as IndexRouteImport } from './ro./routes/app/dashboard
import { Route as AppIndexRouteImport } from './routes/app/index'
import { Route as AppDashboardRouteImport } from './routes/app/dashboard'

const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const AppIndexRoute = AppIndexRouteImport.update({
  id: '/app/',
  path: '/app/',
  getParentRoute: () => rootRouteImport,
} as any)
const AppDashboardRoute = AppDashboardRouteImport.update({
  id: '/app/dashboard',
  path: '/app/dashboard',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/login': typeof LoginRoute
  '/app/dashboard': typeof AppDashboardRoute
  '/app': typeof AppIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/login': typeof LoginRoute
  '/app/dashboard': typeof AppDashboardRoute
  '/app': typeof AppIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/login': typeof LoginRoute
  '/app/dashboard': typeof AppDashboardRoute
  '/app/': typeof AppIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/login' | '/app/dashboard' | '/app'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/login' | '/app/dashboard' | '/app'
  id: '__root__' | '/' | '/login' | '/app/dashboard' | '/app/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  LoginRoute: typeof LoginRoute
  AppDashboardRoute: typeof AppDashboardRoute
  AppIndexRoute: typeof AppIndexRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/app/': {
      id: '/app/'
      path: '/app'
      fullPath: '/app'
      preLoaderRoute: typeof AppIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/app/dashboard': {
      id: '/app/dashboard'
      path: '/app/dashboard'
      fullPath: '/app/dashboard'
      preLoaderRoute: typeof AppDashboardRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  LoginRoute: LoginRoute,
  AppDashboardRoute: AppDashboardRoute,
  AppIndexRoute: AppIndexRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
